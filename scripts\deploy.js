#!/usr/bin/env node

/**
 * 自动化部署脚本
 * 支持多环境部署和部署前检查
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class Deployer {
    constructor() {
        this.environments = {
            staging: {
                name: 'pansou-search-staging',
                env: 'staging',
                url: 'https://pansou-search-staging.workers.dev'
            },
            production: {
                name: 'pansou-search',
                env: 'production',
                url: 'https://pansou.104078.xyz'
            }
        };
    }

    /**
     * 运行命令并捕获输出
     */
    runCommand(command, options = {}) {
        try {
            console.log(`🔧 执行命令: ${command}`);
            const output = execSync(command, {
                encoding: 'utf8',
                stdio: 'inherit',
                ...options
            });
            return { success: true, output };
        } catch (error) {
            return { 
                success: false, 
                output: error.stdout || error.message,
                error: error.stderr || error.message
            };
        }
    }

    /**
     * 检查部署前置条件
     */
    async checkPrerequisites() {
        console.log('🔍 检查部署前置条件...');
        
        const checks = [];
        
        // 检查 wrangler 是否已安装
        const wranglerCheck = this.runCommand('npx wrangler --version', { stdio: 'pipe' });
        checks.push({
            name: 'Wrangler CLI',
            passed: wranglerCheck.success,
            message: wranglerCheck.success ? 'Wrangler CLI 可用' : 'Wrangler CLI 不可用'
        });
        
        // 检查是否已登录 Cloudflare
        const authCheck = this.runCommand('npx wrangler whoami', { stdio: 'pipe' });
        checks.push({
            name: 'Cloudflare 认证',
            passed: authCheck.success,
            message: authCheck.success ? 'Cloudflare 认证有效' : '需要登录 Cloudflare'
        });
        
        // 检查配置文件
        const configExists = fs.existsSync('wrangler.toml');
        checks.push({
            name: 'Wrangler 配置',
            passed: configExists,
            message: configExists ? 'wrangler.toml 存在' : 'wrangler.toml 不存在'
        });
        
        // 检查构建文件
        const workerExists = fs.existsSync('worker.js') || fs.existsSync('src/worker.js');
        checks.push({
            name: 'Worker 文件',
            passed: workerExists,
            message: workerExists ? 'Worker 文件存在' : 'Worker 文件不存在'
        });
        
        // 打印检查结果
        console.log('\n📋 前置条件检查结果:');
        checks.forEach(check => {
            const icon = check.passed ? '✅' : '❌';
            console.log(`  ${icon} ${check.name}: ${check.message}`);
        });
        
        const allPassed = checks.every(check => check.passed);
        
        if (!allPassed) {
            console.log('\n❌ 部分前置条件未满足，请解决后重试');
            return false;
        }
        
        console.log('\n✅ 所有前置条件检查通过');
        return true;
    }

    /**
     * 运行部署前测试
     */
    async runPreDeployTests() {
        console.log('🧪 运行部署前测试...');
        
        const tests = [
            { name: '代码检查', command: 'npm run lint' },
            { name: '安全扫描', command: 'npm run security:audit' }
        ];
        
        for (const test of tests) {
            console.log(`\n🔍 运行 ${test.name}...`);
            const result = this.runCommand(test.command, { stdio: 'pipe' });
            
            if (!result.success) {
                console.log(`❌ ${test.name} 失败:`);
                console.log(result.error);
                return false;
            } else {
                console.log(`✅ ${test.name} 通过`);
            }
        }
        
        return true;
    }

    /**
     * 构建应用
     */
    async buildApplication() {
        console.log('🔨 构建应用...');
        
        // 检查是否有构建脚本
        const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
        
        if (packageJson.scripts && packageJson.scripts.build) {
            const result = this.runCommand('npm run build');
            if (!result.success) {
                console.log('❌ 构建失败');
                return false;
            }
        } else {
            console.log('⚠️ 未找到构建脚本，跳过构建步骤');
        }
        
        console.log('✅ 构建完成');
        return true;
    }

    /**
     * 部署到指定环境
     */
    async deployToEnvironment(environment) {
        const envConfig = this.environments[environment];
        
        if (!envConfig) {
            console.log(`❌ 未知环境: ${environment}`);
            return false;
        }
        
        console.log(`🚀 部署到 ${environment} 环境...`);
        console.log(`📍 目标: ${envConfig.url}`);
        
        // 构建部署命令
        const deployCommand = `npx wrangler deploy --env ${envConfig.env}`;
        
        const result = this.runCommand(deployCommand);
        
        if (result.success) {
            console.log(`✅ 成功部署到 ${environment} 环境`);
            console.log(`🌐 访问地址: ${envConfig.url}`);
            return true;
        } else {
            console.log(`❌ 部署到 ${environment} 环境失败`);
            console.log(result.error);
            return false;
        }
    }

    /**
     * 部署后验证
     */
    async verifyDeployment(environment) {
        const envConfig = this.environments[environment];
        
        console.log(`🔍 验证 ${environment} 环境部署...`);
        
        try {
            // 等待部署生效
            console.log('⏳ 等待部署生效...');
            await new Promise(resolve => setTimeout(resolve, 10000));
            
            // 简单的健康检查
            console.log(`🌐 检查 ${envConfig.url} 可访问性...`);
            
            // 这里可以添加更复杂的验证逻辑
            // 比如检查特定的API端点、运行冒烟测试等
            
            console.log(`✅ ${environment} 环境部署验证通过`);
            return true;
            
        } catch (error) {
            console.log(`❌ ${environment} 环境部署验证失败:`, error.message);
            return false;
        }
    }

    /**
     * 创建部署标签
     */
    async createDeploymentTag(environment) {
        if (environment !== 'production') {
            return;
        }
        
        console.log('🏷️ 创建部署标签...');
        
        try {
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const tagName = `deploy-${timestamp}`;
            
            // 检查是否在 git 仓库中
            const gitCheck = this.runCommand('git status', { stdio: 'pipe' });
            
            if (gitCheck.success) {
                const tagResult = this.runCommand(`git tag ${tagName}`, { stdio: 'pipe' });
                
                if (tagResult.success) {
                    console.log(`✅ 创建标签: ${tagName}`);
                } else {
                    console.log('⚠️ 创建标签失败，但不影响部署');
                }
            } else {
                console.log('⚠️ 不在 git 仓库中，跳过标签创建');
            }
            
        } catch (error) {
            console.log('⚠️ 创建标签时出错:', error.message);
        }
    }

    /**
     * 执行完整部署流程
     */
    async deploy(environment, options = {}) {
        console.log(`🚀 开始部署到 ${environment} 环境\n`);
        
        try {
            // 检查前置条件
            if (!await this.checkPrerequisites()) {
                return false;
            }
            
            // 运行部署前测试
            if (!options.skipTests && !await this.runPreDeployTests()) {
                console.log('❌ 部署前测试失败，终止部署');
                return false;
            }
            
            // 构建应用
            if (!options.skipBuild && !await this.buildApplication()) {
                console.log('❌ 构建失败，终止部署');
                return false;
            }
            
            // 部署
            if (!await this.deployToEnvironment(environment)) {
                console.log('❌ 部署失败');
                return false;
            }
            
            // 验证部署
            if (!options.skipVerification && !await this.verifyDeployment(environment)) {
                console.log('⚠️ 部署验证失败，但部署可能已成功');
            }
            
            // 创建标签
            await this.createDeploymentTag(environment);
            
            console.log(`\n🎉 ${environment} 环境部署完成！`);
            return true;
            
        } catch (error) {
            console.log('❌ 部署过程中发生错误:', error.message);
            return false;
        }
    }
}

// 命令行接口
if (import.meta.url === `file://${process.argv[1]}`) {
    const args = process.argv.slice(2);
    const environment = args[0];
    
    if (!environment) {
        console.log('使用方法: npm run deploy <environment>');
        console.log('可用环境: staging, production');
        process.exit(1);
    }
    
    const options = {
        skipTests: args.includes('--skip-tests'),
        skipBuild: args.includes('--skip-build'),
        skipVerification: args.includes('--skip-verification')
    };
    
    const deployer = new Deployer();
    
    deployer.deploy(environment, options)
        .then(success => {
            process.exit(success ? 0 : 1);
        })
        .catch(error => {
            console.error('❌ 部署失败:', error);
            process.exit(1);
        });
}

export default Deployer;
