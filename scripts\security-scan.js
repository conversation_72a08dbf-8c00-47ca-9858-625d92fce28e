#!/usr/bin/env node

/**
 * 本地安全扫描脚本
 * 运行各种安全检查并生成报告
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class SecurityScanner {
    constructor() {
        this.results = {
            npmAudit: null,
            snykScan: null,
            configCheck: null,
            timestamp: new Date().toISOString()
        };
    }

    /**
     * 运行命令并捕获输出
     */
    runCommand(command, options = {}) {
        try {
            const output = execSync(command, {
                encoding: 'utf8',
                stdio: 'pipe',
                ...options
            });
            return { success: true, output };
        } catch (error) {
            return {
                success: false,
                output: error.stdout || error.message,
                error: error.stderr || error.message
            };
        }
    }

    /**
     * 运行 npm audit
     */
    async runNpmAudit() {
        console.log('🔍 运行 npm audit...');

        const result = this.runCommand('npm audit --json');

        if (result.success) {
            try {
                const auditData = JSON.parse(result.output);
                this.results.npmAudit = {
                    success: true,
                    vulnerabilities: auditData.metadata?.vulnerabilities || {},
                    summary: auditData.metadata
                };

                const vulnCount = Object.values(auditData.metadata?.vulnerabilities || {})
                    .reduce((sum, count) => sum + count, 0);

                if (vulnCount === 0) {
                    console.log('✅ npm audit: 未发现安全漏洞');
                } else {
                    console.log(`⚠️ npm audit: 发现 ${vulnCount} 个安全漏洞`);
                    console.log('详细信息:', auditData.metadata.vulnerabilities);
                }
            } catch (parseError) {
                console.log('✅ npm audit: 未发现安全漏洞 (无JSON输出)');
                this.results.npmAudit = { success: true, vulnerabilities: {} };
            }
        } else {
            console.log('❌ npm audit 执行失败:', result.error);
            this.results.npmAudit = { success: false, error: result.error };
        }
    }

    /**
     * 运行 Snyk 扫描
     */
    async runSnykScan() {
        console.log('🔍 运行 Snyk 扫描...');

        // 检查是否有 Snyk token
        if (!process.env.SNYK_TOKEN) {
            console.log('⚠️ 未设置 SNYK_TOKEN，跳过 Snyk 扫描');
            this.results.snykScan = {
                success: false,
                error: 'SNYK_TOKEN not set'
            };
            return;
        }

        const result = this.runCommand('npx snyk test --json');

        if (result.success) {
            try {
                const snykData = JSON.parse(result.output);
                this.results.snykScan = {
                    success: true,
                    vulnerabilities: snykData.vulnerabilities || [],
                    summary: snykData.summary
                };

                const vulnCount = snykData.vulnerabilities?.length || 0;

                if (vulnCount === 0) {
                    console.log('✅ Snyk: 未发现安全漏洞');
                } else {
                    console.log(`⚠️ Snyk: 发现 ${vulnCount} 个安全漏洞`);
                }
            } catch (parseError) {
                console.log('✅ Snyk: 未发现安全漏洞');
                this.results.snykScan = { success: true, vulnerabilities: [] };
            }
        } else {
            console.log('❌ Snyk 扫描失败:', result.error);
            this.results.snykScan = { success: false, error: result.error };
        }
    }

    /**
     * 检查安全配置
     */
    async checkSecurityConfig() {
        console.log('🔍 检查安全配置...');

        try {
            // 动态导入 ES 模块
            const securityModule = await import('../src/config/security.js');
            const { getSecurityHeaders, generateCSPHeader, isOriginAllowed } = securityModule;

            const checks = [];

            // 检查安全头部
            const headers = getSecurityHeaders('https://example.com', 'production');
            const requiredHeaders = [
                'Content-Security-Policy',
                'X-Content-Type-Options',
                'X-Frame-Options',
                'X-XSS-Protection',
                'Strict-Transport-Security'
            ];

            requiredHeaders.forEach(header => {
                if (headers[header]) {
                    checks.push({ check: `Security header: ${header}`, status: 'pass' });
                } else {
                    checks.push({ check: `Security header: ${header}`, status: 'fail' });
                }
            });

            // 检查 CSP 配置
            const csp = generateCSPHeader('production');
            if (csp && csp.length > 0) {
                checks.push({ check: 'CSP configuration', status: 'pass' });
            } else {
                checks.push({ check: 'CSP configuration', status: 'fail' });
            }

            // 检查 CORS 配置
            const allowedOrigin = isOriginAllowed('https://pansou.104078.xyz', 'production');
            const disallowedOrigin = isOriginAllowed('https://malicious.com', 'production');

            if (allowedOrigin && !disallowedOrigin) {
                checks.push({ check: 'CORS configuration', status: 'pass' });
            } else {
                checks.push({ check: 'CORS configuration', status: 'fail' });
            }

            this.results.configCheck = { success: true, checks };

            const passedChecks = checks.filter(c => c.status === 'pass').length;
            const totalChecks = checks.length;

            console.log(`✅ 安全配置检查: ${passedChecks}/${totalChecks} 项通过`);

            checks.forEach(check => {
                const icon = check.status === 'pass' ? '✅' : '❌';
                console.log(`  ${icon} ${check.check}`);
            });

        } catch (error) {
            console.log('❌ 安全配置检查失败:', error.message);
            this.results.configCheck = { success: false, error: error.message };
        }
    }

    /**
     * 生成安全报告
     */
    generateReport() {
        console.log('\n📊 生成安全扫描报告...');

        const report = {
            timestamp: this.results.timestamp,
            summary: {
                npmAudit: this.results.npmAudit?.success ? 'PASS' : 'FAIL',
                snykScan: this.results.snykScan?.success ? 'PASS' : 'FAIL',
                configCheck: this.results.configCheck?.success ? 'PASS' : 'FAIL'
            },
            details: this.results
        };

        // 保存报告到文件
        const reportPath = path.join(process.cwd(), 'security-report.json');
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

        console.log(`📄 报告已保存到: ${reportPath}`);

        // 打印摘要
        console.log('\n📋 扫描摘要:');
        Object.entries(report.summary).forEach(([check, status]) => {
            const icon = status === 'PASS' ? '✅' : '❌';
            console.log(`  ${icon} ${check}: ${status}`);
        });

        // 计算总体状态
        const allPassed = Object.values(report.summary).every(status => status === 'PASS');

        if (allPassed) {
            console.log('\n🎉 所有安全检查通过！');
            return 0;
        } else {
            console.log('\n⚠️ 部分安全检查未通过，请查看详细报告');
            return 1;
        }
    }

    /**
     * 运行所有安全扫描
     */
    async runAll() {
        console.log('🚀 开始安全扫描...\n');

        await this.runNpmAudit();
        await this.runSnykScan();
        await this.checkSecurityConfig();

        return this.generateReport();
    }
}

// 如果直接运行此脚本
if (import.meta.url.startsWith('file:') && process.argv[1] && import.meta.url.includes(process.argv[1])) {
    const scanner = new SecurityScanner();
    scanner.runAll()
        .then(exitCode => {
            process.exit(exitCode);
        })
        .catch(error => {
            console.error('❌ 安全扫描失败:', error);
            process.exit(1);
        });
}

export default SecurityScanner;

// 直接执行扫描
const scanner = new SecurityScanner();
scanner.runAll()
    .then(exitCode => {
        process.exit(exitCode);
    })
    .catch(error => {
        console.error('❌ 安全扫描失败:', error);
        process.exit(1);
    });
