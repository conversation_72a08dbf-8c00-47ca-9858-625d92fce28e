#!/usr/bin/env node

/**
 * 清理脚本
 * 删除测试过程中生成的临时文件和测试数据
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class Cleaner {
    constructor() {
        this.cleanupItems = [
            // 测试报告文件
            'security-report.json',
            'phase3-validation-report.json',
            'performance-report.json',
            
            // 测试文件
            'tests/validation.test.js',
            'tests/xss-protection.test.js',
            'tests/cors.test.js',
            'tests/csp.test.js',
            
            // 临时构建文件
            'test-local.html',
            
            // 覆盖率报告（如果存在）
            'coverage',
            
            // Lighthouse 临时文件
            '.lighthouseci',
            
            // 其他临时文件
            'lighthouse-report.html',
            'npm-audit-results.json',
            'snyk-results.json',
            'eslint-results.json',
            'complexity-report.json',
            'jscpd-report.json',
            'outdated-deps.json',
            'unused-deps.json'
        ];
        
        this.preserveItems = [
            // 保留的配置文件
            'lighthouserc.js',
            'performance-budget.json',
            '.snyk',
            
            // 保留的脚本文件
            'scripts/security-scan.js',
            'scripts/performance-test.js',
            'scripts/deploy.js',
            'scripts/verify-deployment.js',
            'scripts/validate-phase3.js',
            
            // 保留的文档
            'docs/security-scanning.md',
            'docs/performance-testing.md',
            'docs/deployment.md',
            'docs/github-actions.md',
            
            // 保留的配置
            'config/environments.js',
            'src/config/security.js',
            'src/scripts/utils/validation.js',
            'src/scripts/utils/xss-protection.js',
            
            // 保留的工作流
            '.github/workflows/ci.yml',
            '.github/workflows/security.yml',
            '.github/workflows/quality.yml'
        ];
    }

    /**
     * 检查文件或目录是否存在
     */
    exists(filePath) {
        try {
            return fs.existsSync(filePath);
        } catch {
            return false;
        }
    }

    /**
     * 删除文件或目录
     */
    remove(filePath) {
        try {
            const stats = fs.statSync(filePath);
            if (stats.isDirectory()) {
                fs.rmSync(filePath, { recursive: true, force: true });
                console.log(`🗂️  删除目录: ${filePath}`);
            } else {
                fs.unlinkSync(filePath);
                console.log(`📄 删除文件: ${filePath}`);
            }
            return true;
        } catch (error) {
            console.log(`❌ 删除失败 ${filePath}: ${error.message}`);
            return false;
        }
    }

    /**
     * 清理临时测试文件
     */
    cleanupTestFiles() {
        console.log('🧹 清理临时测试文件...\n');
        
        let removedCount = 0;
        let skippedCount = 0;
        
        for (const item of this.cleanupItems) {
            const fullPath = path.resolve(item);
            
            if (this.exists(fullPath)) {
                if (this.remove(fullPath)) {
                    removedCount++;
                } else {
                    skippedCount++;
                }
            } else {
                console.log(`⚪ 文件不存在: ${item}`);
                skippedCount++;
            }
        }
        
        console.log(`\n📊 清理统计:`);
        console.log(`   删除文件: ${removedCount}`);
        console.log(`   跳过文件: ${skippedCount}`);
        
        return removedCount;
    }

    /**
     * 验证保留的重要文件
     */
    verifyPreservedFiles() {
        console.log('\n🔍 验证保留的重要文件...\n');
        
        let existingCount = 0;
        let missingCount = 0;
        
        for (const item of this.preserveItems) {
            const fullPath = path.resolve(item);
            
            if (this.exists(fullPath)) {
                console.log(`✅ 保留: ${item}`);
                existingCount++;
            } else {
                console.log(`⚠️  缺失: ${item}`);
                missingCount++;
            }
        }
        
        console.log(`\n📊 保留文件统计:`);
        console.log(`   存在文件: ${existingCount}`);
        console.log(`   缺失文件: ${missingCount}`);
        
        return { existingCount, missingCount };
    }

    /**
     * 清理node_modules中的缓存
     */
    cleanupNodeModulesCache() {
        console.log('\n🗂️  清理node_modules缓存...\n');
        
        const cacheDirectories = [
            'node_modules/.cache',
            'node_modules/.vite',
            'node_modules/.webpack',
            'node_modules/.eslintcache'
        ];
        
        let removedCount = 0;
        
        for (const cacheDir of cacheDirectories) {
            if (this.exists(cacheDir)) {
                if (this.remove(cacheDir)) {
                    removedCount++;
                }
            }
        }
        
        console.log(`📊 清理缓存目录: ${removedCount}`);
        return removedCount;
    }

    /**
     * 生成清理报告
     */
    generateCleanupReport(stats) {
        console.log('\n📋 清理报告:');
        console.log('=' .repeat(50));
        console.log(`🗑️  删除的临时文件: ${stats.removedFiles}`);
        console.log(`🗂️  清理的缓存目录: ${stats.removedCaches}`);
        console.log(`✅ 保留的重要文件: ${stats.preservedFiles.existingCount}`);
        console.log(`⚠️  缺失的重要文件: ${stats.preservedFiles.missingCount}`);
        console.log('=' .repeat(50));
        
        if (stats.preservedFiles.missingCount > 0) {
            console.log('\n⚠️  注意: 有些重要文件缺失，可能需要检查');
        }
        
        if (stats.removedFiles > 0 || stats.removedCaches > 0) {
            console.log('\n🎉 清理完成！项目目录已整理');
        } else {
            console.log('\n✨ 项目目录已经很干净，无需清理');
        }
    }

    /**
     * 执行完整清理
     */
    async runCleanup() {
        console.log('🚀 开始清理临时测试文件...\n');
        
        try {
            // 清理临时测试文件
            const removedFiles = this.cleanupTestFiles();
            
            // 验证保留的重要文件
            const preservedFiles = this.verifyPreservedFiles();
            
            // 清理缓存
            const removedCaches = this.cleanupNodeModulesCache();
            
            // 生成报告
            this.generateCleanupReport({
                removedFiles,
                removedCaches,
                preservedFiles
            });
            
            return true;
            
        } catch (error) {
            console.error('❌ 清理过程中发生错误:', error);
            return false;
        }
    }
}

// 直接执行清理
const cleaner = new Cleaner();
cleaner.runCleanup()
    .then(success => {
        process.exit(success ? 0 : 1);
    })
    .catch(error => {
        console.error('❌ 清理失败:', error);
        process.exit(1);
    });
