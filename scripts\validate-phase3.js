#!/usr/bin/env node

/**
 * 第三阶段验证脚本
 * 验证安全加固和CI/CD流程的完整实施
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class Phase3Validator {
    constructor() {
        this.results = {
            securityTests: [],
            cicdTests: [],
            integrationTests: [],
            timestamp: new Date().toISOString()
        };
    }

    /**
     * 运行命令并捕获输出
     */
    runCommand(command, options = {}) {
        try {
            const output = execSync(command, {
                encoding: 'utf8',
                stdio: 'pipe',
                ...options
            });
            return { success: true, output };
        } catch (error) {
            return { 
                success: false, 
                output: error.stdout || error.message,
                error: error.stderr || error.message
            };
        }
    }

    /**
     * 验证安全加固实施
     */
    async validateSecurityHardening() {
        console.log('🔒 验证安全加固实施...\n');
        
        const tests = [
            {
                name: '输入验证测试',
                command: 'node tests/validation.test.js',
                description: '验证zod输入验证器的实施'
            },
            {
                name: 'XSS防护测试',
                command: 'node tests/xss-protection.test.js',
                description: '验证XSS防护机制的有效性'
            },
            {
                name: 'CORS配置测试',
                command: 'node tests/cors.test.js',
                description: '验证CORS配置的安全性'
            },
            {
                name: 'CSP策略测试',
                command: 'node tests/csp.test.js',
                description: '验证CSP头部策略的配置'
            },
            {
                name: '安全扫描测试',
                command: 'npm run security:audit',
                description: '验证依赖安全扫描工具'
            }
        ];
        
        let passedTests = 0;
        
        for (const test of tests) {
            console.log(`🧪 ${test.name}...`);
            console.log(`   ${test.description}`);
            
            const result = this.runCommand(test.command);
            
            if (result.success) {
                console.log(`   ✅ 通过\n`);
                passedTests++;
                this.results.securityTests.push({
                    name: test.name,
                    status: 'PASS',
                    description: test.description
                });
            } else {
                console.log(`   ❌ 失败: ${result.error}\n`);
                this.results.securityTests.push({
                    name: test.name,
                    status: 'FAIL',
                    description: test.description,
                    error: result.error
                });
            }
        }
        
        console.log(`📊 安全加固验证结果: ${passedTests}/${tests.length} 通过\n`);
        return passedTests === tests.length;
    }

    /**
     * 验证CI/CD流程配置
     */
    async validateCICDPipeline() {
        console.log('🚀 验证CI/CD流程配置...\n');
        
        const checks = [
            {
                name: 'GitHub Actions工作流',
                check: () => {
                    const files = [
                        '.github/workflows/ci.yml',
                        '.github/workflows/security.yml',
                        '.github/workflows/quality.yml'
                    ];
                    return files.every(file => fs.existsSync(file));
                },
                description: '检查GitHub Actions工作流文件是否存在'
            },
            {
                name: '部署脚本',
                check: () => {
                    const files = [
                        'scripts/deploy.js',
                        'scripts/verify-deployment.js'
                    ];
                    return files.every(file => fs.existsSync(file));
                },
                description: '检查部署相关脚本是否存在'
            },
            {
                name: '安全扫描配置',
                check: () => {
                    const files = [
                        '.snyk',
                        'scripts/security-scan.js'
                    ];
                    return files.every(file => fs.existsSync(file));
                },
                description: '检查安全扫描配置文件是否存在'
            },
            {
                name: '性能测试配置',
                check: () => {
                    const files = [
                        'lighthouserc.js',
                        'performance-budget.json',
                        'scripts/performance-test.js'
                    ];
                    return files.every(file => fs.existsSync(file));
                },
                description: '检查性能测试配置文件是否存在'
            },
            {
                name: '环境配置',
                check: () => {
                    const files = [
                        'config/environments.js',
                        'wrangler.toml'
                    ];
                    return files.every(file => fs.existsSync(file));
                },
                description: '检查环境配置文件是否存在'
            },
            {
                name: 'package.json脚本',
                check: () => {
                    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
                    const requiredScripts = [
                        'security:scan',
                        'deploy:staging',
                        'deploy:production',
                        'verify:staging',
                        'verify:production'
                    ];
                    return requiredScripts.every(script => packageJson.scripts[script]);
                },
                description: '检查package.json中的必需脚本是否存在'
            }
        ];
        
        let passedChecks = 0;
        
        for (const check of checks) {
            console.log(`🔍 ${check.name}...`);
            console.log(`   ${check.description}`);
            
            try {
                const passed = check.check();
                if (passed) {
                    console.log(`   ✅ 通过\n`);
                    passedChecks++;
                    this.results.cicdTests.push({
                        name: check.name,
                        status: 'PASS',
                        description: check.description
                    });
                } else {
                    console.log(`   ❌ 失败\n`);
                    this.results.cicdTests.push({
                        name: check.name,
                        status: 'FAIL',
                        description: check.description
                    });
                }
            } catch (error) {
                console.log(`   ❌ 失败: ${error.message}\n`);
                this.results.cicdTests.push({
                    name: check.name,
                    status: 'FAIL',
                    description: check.description,
                    error: error.message
                });
            }
        }
        
        console.log(`📊 CI/CD流程验证结果: ${passedChecks}/${checks.length} 通过\n`);
        return passedChecks === checks.length;
    }

    /**
     * 验证集成效果
     */
    async validateIntegration() {
        console.log('🔗 验证集成效果...\n');
        
        const integrationTests = [
            {
                name: '安全配置集成',
                test: async () => {
                    // 验证安全配置是否正确集成到应用中
                    const securityModule = await import('../src/config/security.js');
                    const { getSecurityHeaders, generateCSPHeader } = securityModule;
                    
                    const headers = getSecurityHeaders('https://example.com', 'production');
                    const csp = generateCSPHeader('production');
                    
                    return headers && csp && Object.keys(headers).length > 0;
                },
                description: '验证安全配置模块是否正确集成'
            },
            {
                name: '验证器集成',
                test: async () => {
                    // 验证新的验证器是否正确集成
                    const validationModule = await import('../src/scripts/utils/validation.js');
                    const { EnhancedValidator } = validationModule;
                    
                    try {
                        const result = EnhancedValidator.validateSearchKeyword('测试');
                        return result === '测试';
                    } catch {
                        return false;
                    }
                },
                description: '验证增强验证器是否正确集成'
            },
            {
                name: 'XSS防护集成',
                test: async () => {
                    // 验证XSS防护是否正确集成
                    const xssModule = await import('../src/scripts/utils/xss-protection.js');
                    const { XSSProtection } = xssModule;
                    
                    const maliciousInput = '<script>alert("xss")</script>';
                    const cleaned = XSSProtection.sanitizeHTML(maliciousInput);
                    
                    return !cleaned.includes('<script>');
                },
                description: '验证XSS防护模块是否正确集成'
            }
        ];
        
        let passedTests = 0;
        
        for (const test of integrationTests) {
            console.log(`🧪 ${test.name}...`);
            console.log(`   ${test.description}`);
            
            try {
                const passed = await test.test();
                if (passed) {
                    console.log(`   ✅ 通过\n`);
                    passedTests++;
                    this.results.integrationTests.push({
                        name: test.name,
                        status: 'PASS',
                        description: test.description
                    });
                } else {
                    console.log(`   ❌ 失败\n`);
                    this.results.integrationTests.push({
                        name: test.name,
                        status: 'FAIL',
                        description: test.description
                    });
                }
            } catch (error) {
                console.log(`   ❌ 失败: ${error.message}\n`);
                this.results.integrationTests.push({
                    name: test.name,
                    status: 'FAIL',
                    description: test.description,
                    error: error.message
                });
            }
        }
        
        console.log(`📊 集成验证结果: ${passedTests}/${integrationTests.length} 通过\n`);
        return passedTests === integrationTests.length;
    }

    /**
     * 生成验证报告
     */
    generateReport() {
        console.log('📊 生成第三阶段验证报告...\n');
        
        const totalTests = this.results.securityTests.length + 
                          this.results.cicdTests.length + 
                          this.results.integrationTests.length;
        
        const passedTests = [
            ...this.results.securityTests,
            ...this.results.cicdTests,
            ...this.results.integrationTests
        ].filter(test => test.status === 'PASS').length;
        
        const report = {
            timestamp: this.results.timestamp,
            summary: {
                totalTests,
                passedTests,
                successRate: Math.round((passedTests / totalTests) * 100)
            },
            details: this.results
        };
        
        // 保存报告到文件
        const reportPath = path.join(process.cwd(), 'phase3-validation-report.json');
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        
        console.log(`📄 报告已保存到: ${reportPath}\n`);
        
        // 打印摘要
        console.log('🎯 第三阶段验证摘要:');
        console.log(`   总测试数: ${totalTests}`);
        console.log(`   通过测试: ${passedTests}`);
        console.log(`   成功率: ${report.summary.successRate}%`);
        
        console.log('\n📋 分类结果:');
        console.log(`   🔒 安全加固: ${this.results.securityTests.filter(t => t.status === 'PASS').length}/${this.results.securityTests.length}`);
        console.log(`   🚀 CI/CD流程: ${this.results.cicdTests.filter(t => t.status === 'PASS').length}/${this.results.cicdTests.length}`);
        console.log(`   🔗 集成效果: ${this.results.integrationTests.filter(t => t.status === 'PASS').length}/${this.results.integrationTests.length}`);
        
        const allPassed = passedTests === totalTests;
        
        if (allPassed) {
            console.log('\n🎉 第三阶段验证全部通过！安全加固和CI/CD流程实施成功！');
        } else {
            console.log('\n⚠️ 部分验证未通过，请查看详细报告进行修复');
        }
        
        return allPassed ? 0 : 1;
    }

    /**
     * 运行完整验证
     */
    async runValidation() {
        console.log('🚀 开始第三阶段完整验证...\n');
        
        try {
            await this.validateSecurityHardening();
            await this.validateCICDPipeline();
            await this.validateIntegration();
            
            return this.generateReport();
            
        } catch (error) {
            console.error('❌ 验证过程中发生错误:', error);
            return 1;
        }
    }
}

// 直接执行验证
const validator = new Phase3Validator();
validator.runValidation()
    .then(exitCode => {
        process.exit(exitCode);
    })
    .catch(error => {
        console.error('❌ 第三阶段验证失败:', error);
        process.exit(1);
    });
